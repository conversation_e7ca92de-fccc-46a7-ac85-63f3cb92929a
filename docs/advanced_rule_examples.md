# 高级规则配置示例

本文档展示如何使用 `conditional_check` 来实现复杂的校验逻辑，替代原有的专用检查函数。

## 1. 双签发检查

**原来的专用函数**：`dual_signature_check`

**用 conditional_check 实现**：
```json
{
  "rule_id": "D1-8",
  "category": "不合格",
  "description": "此工作票应双签发而未双签发",
  "field_path": "签发.工作票签发人签名",
  "condition": "conditional_check",
  "if_condition": {
    "field_path": "签发.工作票签发人签名",
    "condition": "not_empty"
  },
  "then_check": {
    "field_path": "签发.工作票会签人签名",
    "condition": "not_empty",
    "negate": false
  },
  "evidence_template": "要求双签发，当前状态：签发人{value}，会签人未签或缺失",
  "suggestion": "请工作票签发人和会签人共同签名"
}
```

## 2. 字段值有效性检查

**原来的专用函数**：`not_empty_or_invalid`

**用 conditional_check 实现**：
```json
{
  "rule_id": "D1-6-1",
  "category": "不合格",
  "description": "工作任务填写不明确或错漏",
  "field_path": "工作任务",
  "condition": "conditional_check",
  "if_condition": {
    "field_path": "工作任务",
    "condition": "not_empty"
  },
  "then_check": {
    "field_path": "工作任务",
    "condition": "contains_keywords",
    "keywords": ["无"],
    "negate": true
  },
  "evidence_template": "字段工作任务内容为空或为无效值：{value}",
  "suggestion": "请详细、明确地填写工作任务"
}
```

## 3. 班组签名数量检查

**原来的专用函数**：`team_signature_check`

**用 conditional_check 实现**：
```json
{
  "rule_id": "D1-16-7",
  "category": "不合格",
  "description": "安全交代中工作班人员全体漏签名",
  "field_path": "安全交代.工作班人员签名",
  "condition": "conditional_check",
  "if_condition": {
    "field_path": "工作班人员（不包括工作负责人）",
    "condition": "not_empty"
  },
  "then_check": {
    "field_path": "安全交代.工作班人员签名",
    "condition": "numeric_compare",
    "compare_with_field": "工作班人员（不包括工作负责人）",
    "compare_type": "equals_count"
  },
  "evidence_template": "工作班有成员，但安全交代中签名数量不匹配",
  "suggestion": "请全体工作班成员在安全交代后签名"
}
```

## 4. 变更手续完整性检查

**原来的专用函数**：`change_procedure_check`

**用多个简单规则实现**：
```json
[
  {
    "rule_id": "D1-10-1",
    "category": "不合格",
    "description": "工作负责人变更手续不完整-缺少签发人签名",
    "field_path": "工作变更.工作负责人变更.工作票签发人签名",
    "condition": "conditional_check",
    "if_condition": {
      "field_path": "工作变更.工作负责人变更.原工作负责人签名",
      "condition": "not_empty"
    },
    "then_check": {
      "field_path": "工作变更.工作负责人变更.工作票签发人签名",
      "condition": "not_empty"
    },
    "evidence_template": "有负责人变更记录，但缺少工作票签发人签名",
    "suggestion": "请补全所有变更手续和签名"
  },
  {
    "rule_id": "D1-10-2",
    "category": "不合格",
    "description": "工作负责人变更手续不完整-缺少变更时间",
    "field_path": "工作变更.工作负责人变更.同意变更时间",
    "condition": "conditional_check",
    "if_condition": {
      "field_path": "工作变更.工作负责人变更.原工作负责人签名",
      "condition": "not_empty"
    },
    "then_check": {
      "field_path": "工作变更.工作负责人变更.同意变更时间",
      "condition": "not_empty"
    },
    "evidence_template": "有负责人变更记录，但缺少同意变更时间",
    "suggestion": "请补全所有变更手续和签名"
  }
]
```

## 5. negate 参数的使用

### 检查不包含关键词
```json
{
  "rule_id": "D1-7-1",
  "category": "不合格",
  "description": "硬件安全措施可能不完备",
  "field_path": "工作要求的安全措施.硬件及工作环境,应设遮栏、应挂标示牌位置",
  "condition": "contains_keywords",
  "keywords": ["遮栏", "标示牌", "标志牌", "警示带", "误碰", "隔离"],
  "match_mode": "any",
  "negate": true,
  "evidence_template": "硬件安措内容为：{value}，未找到必要的安全措施关键词",
  "suggestion": "请根据现场情况，补充必要的硬件安全措施"
}
```

### 检查格式不匹配
```json
{
  "rule_id": "D1-3-4-2",
  "category": "不合格", 
  "description": "关键词工作许可时间填写不规范或不清",
  "field_path": "工作许可.许可时间",
  "condition": "matches_regex",
  "regex": "\\d{1,2}\\s*月\\s*\\d{1,2}\\s*日\\s*\\d{1,2}\\s*时\\s*\\d{1,2}\\s*分",
  "negate": true,
  "evidence_template": "工作许可.许可时间为：{value}，时间信息格式不标准",
  "suggestion": "请核对并确保工作许可时间清晰、完整"
}
```

## 优势

使用 `conditional_check` 和 `negate` 参数的优势：

1. **统一性**：所有规则都使用相同的基础条件类型
2. **灵活性**：可以组合多个简单条件实现复杂逻辑
3. **可读性**：规则配置更加清晰明了
4. **可维护性**：减少代码复杂度，便于维护和扩展

## 注意事项

1. **性能考虑**：复杂的条件组合可能影响性能，建议合理设计规则
2. **逻辑清晰**：确保 if-then 逻辑清晰，避免过于复杂的嵌套
3. **测试验证**：新规则配置后务必进行充分测试
