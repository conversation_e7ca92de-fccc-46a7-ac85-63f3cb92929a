# 工作票校验规则文件使用规范

## 概述

本规范定义了工作票自动校验系统的规则文件格式，允许业务人员通过配置JSON格式的规则文件来定义各种校验逻辑，无需修改代码即可调整校验规则。

## 规则文件格式

规则文件采用JSON格式，包含一个规则数组，每个规则对象包含以下字段：

### 基本字段

- **rule_id** (string, 必填): 规则唯一标识符，建议使用格式如 "D1-1", "D2-6" 等
- **category** (string, 必填): 规则类别，可选值：
  - "不合格": 严重问题，工作票不能使用
  - "不规范": 格式问题，需要修正但不影响使用
  - "请人工确认": 需要人工判断的问题
- **description** (string, 必填): 问题描述，用于输出报告
- **field_path** (string, 必填): 要检查的字段路径，支持嵌套路径如 "工作许可.许可时间"
- **condition** (string, 必填): 校验条件类型
- **evidence_template** (string, 必填): 证据模板，支持变量替换
- **suggestion** (string, 必填): 建议操作

### 支持的校验条件类型

#### 1. 空值检查 (not_empty)
检查字段是否为空或未填写。

```json
{
  "rule_id": "D1-9",
  "category": "不合格",
  "description": "工作许可时间未填写",
  "field_path": "工作许可.许可时间",
  "condition": "not_empty",
  "evidence_template": "字段"{field_path}"为空",
  "suggestion": "请工作许可人填写许可时间"
}
```

#### 2. 正则表达式匹配 (matches_regex)
使用正则表达式检查字段格式。

```json
{
  "rule_id": "D2-6",
  "category": "不规范", 
  "description": "时间格式不规范",
  "field_path": "计划工作时间.开始时间",
  "condition": "matches_regex",
  "regex": "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$",
  "evidence_template": "时间格式为"{value}"，不符合标准格式",
  "suggestion": "请使用YYYY-MM-DD HH:MM格式"
}
```

#### 3. 包含关键字 (contains_keywords)
检查字段是否包含指定的关键字。

```json
{
  "rule_id": "D1-7-1",
  "category": "不合格",
  "description": "硬件安全措施可能不完备",
  "field_path": "工作要求的安全措施.硬件及工作环境,应设遮栏、应挂标示牌位置",
  "condition": "contains_keywords",
  "keywords": ["遮栏", "标示牌", "标志牌", "警示带", "误碰", "隔离"],
  "match_mode": "any",
  "evidence_template": "硬件安措内容为："{value}"，未找到必要的安全措施关键词",
  "suggestion": "请根据现场情况，补充必要的硬件安全措施"
}
```

#### 4. 日期时间检查 (datetime_check)
检查日期时间的有效性和逻辑关系。

```json
{
  "rule_id": "D1-2",
  "category": "不合格",
  "description": "计划工作时间已过期",
  "field_path": "计划工作时间.结束时间",
  "condition": "datetime_check",
  "datetime_format": "%Y-%m-%d %H:%M",
  "check_type": "not_past",
  "evidence_template": "计划结束时间({value})早于当前时间",
  "suggestion": "请修改计划工作时间或作废此票"
}
```

#### 5. 数值比较 (numeric_compare)
比较数值字段。

```json
{
  "rule_id": "D1-5",
  "category": "不合格", 
  "description": "工作班人数与申报总数不符",
  "field_path": "工作负责人及工作班人员总数",
  "condition": "numeric_compare",
  "compare_with_field": "工作班人员（不包括工作负责人）",
  "compare_type": "equals_count_plus_one",
  "evidence_template": "申报总人数为{value}，但实际人数不符",
  "suggestion": "请核对工作班成员名单及申报总数"
}
```

#### 6. 条件组合 (conditional_check)
基于其他字段的值来决定是否执行检查。

```json
{
  "rule_id": "D1-1",
  "category": "不合格",
  "description": "错用工作票类型",
  "field_path": "工作票类型",
  "condition": "conditional_check",
  "if_condition": {
    "field_path": "工作任务",
    "condition": "contains_keywords",
    "keywords": ["紧急", "紧急消缺"],
    "match_mode": "any"
  },
  "then_check": {
    "condition": "contains_keywords", 
    "keywords": ["紧急抢修"],
    "match_mode": "any",
    "negate": false
  },
  "evidence_template": "工作任务包含紧急字样，但工作票类型为"{value}"",
  "suggestion": "紧急工作应使用紧急抢修工作票"
}
```

### 高级特性

#### negate参数
`negate` 参数用于反转检查结果，默认为 `false`。

- `negate: false` (默认)：条件满足时通过，不满足时报错
- `negate: true`：条件不满足时通过，满足时报错

**示例：检查是否不包含关键词**
```json
{
  "rule_id": "D1-7-1",
  "condition": "contains_keywords",
  "keywords": ["遮栏", "标示牌"],
  "negate": true,
  "evidence_template": "未找到必要的安全措施关键词"
}
```
这个规则会在字段**不包含**指定关键词时报错。

#### 变量替换
在 evidence_template 中可以使用以下变量：
- `{value}`: 当前检查字段的值
- `{field_path}`: 字段路径
- `{rule_id}`: 规则ID

#### 字段路径语法
- 简单字段: `"工作票类型"`
- 嵌套字段: `"工作许可.许可时间"`
- 数组字段: `"工作班人员（不包括工作负责人）"`
- 数组索引: `"工作间断.0.工作间断时间"`

## 使用示例

完整的规则文件示例请参考 `rules_example.json`。

## 注意事项

1. 规则ID必须唯一，建议按照附录D的编号规范命名
2. 正则表达式中的反斜杠需要双重转义
3. 关键字匹配默认不区分大小写
4. 日期时间格式必须与解析器输出的格式一致
5. 规则执行顺序按照在文件中的定义顺序
